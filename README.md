# Rishi Core

A TypeScript Node.js application with automated setup, build, and run scripts.

## 🚀 Quick Start

### Prerequisites

- **Node.js** (version 18 or higher) - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)

### Setup

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd rishi-core
   ```

2. **Run the setup script**:
   ```bash
   chmod +x dev script/*.sh
   ./dev setup
   ```

   Or directly:
   ```bash
   script/setup.sh
   ```

   This will:
   - Check Node.js and npm installation
   - Install all dependencies
   - Create necessary directories
   - Build the project to verify everything works

### Development

#### Using npm scripts:
```bash
# Development mode with hot reload
npm run dev

# Build for production
npm run build

# Run the built application
npm start

# Code quality and formatting
npm run lint          # Check for linting errors
npm run lint:fix      # Fix linting errors automatically
npm run format        # Format code with Prettier
npm run format:check  # Check if code is properly formatted
npm run check         # Run both lint and format check
npm run fix           # Fix both linting and formatting issues
```

#### Using convenience scripts:
```bash
# Using the dev helper (recommended)
./dev run                    # Lint, build and run the project
./dev run --skip-lint        # Skip linting step
./dev run --lint-only        # Only run linting, don't build/run
./dev run --lint-fix         # Fix linting and formatting issues
./dev setup                  # Set up the development environment

# Or use scripts directly
script/run.sh                # Lint, build and run the project
script/setup.sh              # Set up the development environment
```

## 📁 Current Project Structure

```
rishi-core/

├── .gitignore
├── .prettierignore
├── .prettierrc
├── dev
├── eslint.config.js
├── package-lock.json
├── package.json
├── README.md
├── tsconfig.json
├── .git/...
├── dist/...
├── logs/...
├── node_modules/...
├── script/
│   ├── run.sh
│   └── setup.sh
└── src/
    ├── App.ts
    ├── controllers/
    │   └── dataController.ts
    └── routes/
        └── routes.ts
```

## 🛠️ Scripts Documentation

### dev (Development Helper)
**Purpose**: Convenience wrapper for all development scripts.

**What it does**:
- Provides a simple interface to run setup and development scripts
- Shows helpful usage information
- Passes through all arguments to the underlying scripts

**Usage**:
```bash
chmod +x dev
./dev                    # Show help
./dev setup              # Run setup script
./dev run                # Run development script
./dev run --lint-fix     # Run with lint fix option
```

### script/setup.sh
**Purpose**: Sets up the development environment from scratch.

**What it does**:
- Verifies Node.js and npm installation
- Checks Node.js version compatibility
- Installs project dependencies
- Creates necessary directories
- Performs initial build to verify setup
- Provides helpful next steps

**Usage**:
```bash
chmod +x script/setup.sh
script/setup.sh
```

### script/run.sh
**Purpose**: Lints, builds and runs the application with various options.

**What it does**:
- Checks if dependencies are installed
- Runs ESLint to check code quality (unless skipped)
- Cleans previous build output
- Compiles TypeScript to JavaScript
- Verifies build success
- Shows output file information
- Runs the compiled application
- Provides clear output separation

**Usage**:
```bash
chmod +x script/run.sh
script/run.sh                # Full process: lint → build → run
script/run.sh --skip-lint    # Skip linting step
script/run.sh --lint-only    # Only run linting check
script/run.sh --lint-fix     # Fix linting and formatting issues
```

## 🔧 Configuration

### TypeScript Configuration (tsconfig.json)
- **Target**: ESNext
- **Module**: NodeNext
- **Source Directory**: `src/`
- **Output Directory**: `dist/`
- **Source Maps**: Enabled for debugging

### Package Configuration (package.json)
- **Main Entry**: `App.js`
- **Scripts**: dev, build, start, lint, format
- **Dependencies**: TypeScript, ts-node, @types/node, Express
- **Dev Dependencies**: ESLint, Prettier, TypeScript ESLint plugins

## 🎯 Code Quality Tools

### ESLint Configuration
- **Parser**: @typescript-eslint/parser
- **Plugins**: @typescript-eslint, prettier
- **Rules**:
  - TypeScript-specific rules for better code quality
  - Prettier integration for consistent formatting
  - Custom rules for semicolons, quotes, and unused variables
- **Ignored**: dist/, node_modules/, *.js, *.mjs

### Prettier Configuration
- **Semi**: true (always use semicolons)
- **Single Quote**: true (prefer single quotes)
- **Trailing Comma**: all (add trailing commas)
- **Tab Width**: 2 spaces
- **Print Width**: 100 characters
- **Ignored**: dist/, node_modules/

## 📝 Development Workflow

1. **Initial Setup**:
   ```bash
   ./dev setup
   ```

2. **Development**:
   ```bash
   npm run dev  # Hot reload development
   ```

3. **Code Quality Checks**:
   ```bash
   npm run check    # Check linting and formatting
   npm run fix      # Fix linting and formatting issues
   ```

4. **Testing Changes**:
   ```bash
   ./dev run     # Lint, build and run the application
   ```

4. **Production Build**:
   ```bash
   npm run build
   npm start
   ```

## 🚫 What's Ignored (.gitignore)

The `.gitignore` file excludes:
- **Build outputs**: `dist/`, `build/`
- **Dependencies**: `node_modules/`
- **IDE files**: `.vscode/`, `.idea/`
- **OS files**: `.DS_Store`, `Thumbs.db`
- **Environment files**: `.env.*` (except `.env.example`)
- **Logs and cache**: `*.log`, `.cache`
- **Temporary files**: `*.tmp`, `*.temp`
- **Database files**: `*.db`, `*.sqlite`
- **Archive files**: `*.zip`, `*.tar.gz`

## 🐛 Troubleshooting

### Common Issues

1. **"Node.js is not installed"**
   - Install Node.js from [nodejs.org](https://nodejs.org/)
   - Restart your terminal after installation

2. **"Dependencies not found"**
   - Run `script/setup.sh` to install dependencies
   - Or manually run `npm install`

3. **"Linting failed"**
   - Check ESLint errors in the output
   - Run `script/run.sh --lint-fix` to auto-fix many issues
   - Run `npm run fix` to fix both linting and formatting

4. **"Build failed"**
   - Check TypeScript errors in the output
   - Verify all source files are valid TypeScript
   - Run `npm run dev` to see detailed error messages

5. **Permission denied on scripts**
   - Make scripts executable: `chmod +x script/*.sh`

### Getting Help

If you encounter issues:
1. Check the error messages carefully
2. Verify Node.js version: `node --version`
3. Try cleaning and rebuilding: `rm -rf dist node_modules && script/setup.sh`


