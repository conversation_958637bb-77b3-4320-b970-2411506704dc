# Rishi Core

A TypeScript Node.js application with automated setup, build, and run scripts.

## 🚀 Quick Start

### Prerequisites

- **Node.js** (version 18 or higher) - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)

### Setup

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd rishi-core
   ```

2. **Run the setup script**:
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

   This will:
   - Check Node.js and npm installation
   - Install all dependencies
   - Create necessary directories
   - Build the project to verify everything works

### Development

#### Using npm scripts:
```bash
# Development mode with hot reload
npm run dev

# Build for production
npm run build

# Run the built application
npm start
```

#### Using convenience scripts:
```bash
# Build the project
./build.sh

# Build and run the project
./run.sh
```

## 📁 Project Structure

```
rishi-core/
├── src/                 # TypeScript source files
│   └── App.ts          # Main application entry point
├── dist/               # Compiled JavaScript output (auto-generated)
├── node_modules/       # Dependencies (auto-generated)
├── scripts/            # Build and utility scripts
├── package.json        # Project configuration and dependencies
├── tsconfig.json       # TypeScript configuration
├── .gitignore         # Git ignore rules
├── setup.sh           # Environment setup script
├── build.sh           # Build script
├── run.sh             # Run script
└── README.md          # This file
```

## 🛠️ Scripts Documentation

### setup.sh
**Purpose**: Sets up the development environment from scratch.

**What it does**:
- Verifies Node.js and npm installation
- Checks Node.js version compatibility
- Installs project dependencies
- Creates necessary directories
- Performs initial build to verify setup
- Provides helpful next steps

**Usage**:
```bash
chmod +x setup.sh
./setup.sh
```

### build.sh
**Purpose**: Builds the TypeScript project for production.

**What it does**:
- Checks if dependencies are installed
- Cleans previous build output
- Compiles TypeScript to JavaScript
- Verifies build success
- Shows output file information

**Usage**:
```bash
chmod +x build.sh
./build.sh
```

### run.sh
**Purpose**: Builds (if needed) and runs the application.

**What it does**:
- Checks if dependencies are installed
- Builds the project if no build exists
- Runs the compiled application
- Provides clear output separation

**Usage**:
```bash
chmod +x run.sh
./run.sh
```

## 🔧 Configuration

### TypeScript Configuration (tsconfig.json)
- **Target**: ESNext
- **Module**: NodeNext
- **Source Directory**: `src/`
- **Output Directory**: `dist/`
- **Source Maps**: Enabled for debugging

### Package Configuration (package.json)
- **Main Entry**: `App.js`
- **Scripts**: dev, build, start
- **Dependencies**: TypeScript, ts-node, @types/node

## 📝 Development Workflow

1. **Initial Setup**:
   ```bash
   ./setup.sh
   ```

2. **Development**:
   ```bash
   npm run dev  # Hot reload development
   ```

3. **Testing Changes**:
   ```bash
   ./build.sh   # Build to check for errors
   ./run.sh     # Test the built application
   ```

4. **Production Build**:
   ```bash
   npm run build
   npm start
   ```

## 🚫 What's Ignored (.gitignore)

The `.gitignore` file excludes:
- **Build outputs**: `dist/`, `build/`
- **Dependencies**: `node_modules/`
- **IDE files**: `.vscode/`, `.idea/`
- **OS files**: `.DS_Store`, `Thumbs.db`
- **Environment files**: `.env.*` (except `.env.example`)
- **Logs and cache**: `*.log`, `.cache`
- **Temporary files**: `*.tmp`, `*.temp`
- **Database files**: `*.db`, `*.sqlite`
- **Archive files**: `*.zip`, `*.tar.gz`

## 🐛 Troubleshooting

### Common Issues

1. **"Node.js is not installed"**
   - Install Node.js from [nodejs.org](https://nodejs.org/)
   - Restart your terminal after installation

2. **"Dependencies not found"**
   - Run `./setup.sh` to install dependencies
   - Or manually run `npm install`

3. **"Build failed"**
   - Check TypeScript errors in the output
   - Verify all source files are valid TypeScript
   - Run `npm run dev` to see detailed error messages

4. **Permission denied on scripts**
   - Make scripts executable: `chmod +x *.sh`

### Getting Help

If you encounter issues:
1. Check the error messages carefully
2. Verify Node.js version: `node --version`
3. Try cleaning and rebuilding: `rm -rf dist node_modules && ./setup.sh`

## 📄 License

This project is licensed under the ISC License.
