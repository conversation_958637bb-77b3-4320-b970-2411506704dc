#!/bin/bash

# Rishi Core - Setup Script
# This script sets up the development environment for the Rishi Core project

set -e  # Exit on any error

echo "🚀 Setting up Rishi Core development environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js (version 18 or higher) first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
    echo "⚠️  Node.js version $NODE_VERSION detected. Version $REQUIRED_VERSION or higher is recommended."
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js $(node --version) and npm $(npm --version) are available"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Verify TypeScript installation
if ! npx tsc --version &> /dev/null; then
    echo "❌ TypeScript installation failed"
    exit 1
fi

echo "✅ TypeScript $(npx tsc --version) is available"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p dist
mkdir -p logs

# Build the project to verify everything works
echo "🔨 Building project to verify setup..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Available commands:"
echo "  npm run dev     - Run in development mode with hot reload"
echo "  npm run build   - Build the project for production"
echo "  npm run start   - Run the built project"
echo ""
echo "You can also use the convenience script:"
echo "  ./run.sh        - Build and run the project"
echo ""
echo "Happy coding! 🚀"
