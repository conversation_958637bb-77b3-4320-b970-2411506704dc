#!/bin/bash

# Rishi Core - Node.js Version Helper
# This script ensures the correct Node.js version is being used

# Load nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# Check if nvm is available
if ! command -v nvm &> /dev/null; then
    echo "❌ nvm is not installed. Please run script/setup.sh first."
    exit 1
fi

# Use Node.js version from .nvmrc
if [ -f ".nvmrc" ]; then
    echo "📦 Switching to Node.js version from .nvmrc..."
    nvm use
    echo "✅ Node.js $(node --version) is now active"
    echo ""
    echo "💡 To make this permanent for new terminal sessions:"
    echo "   Add 'nvm use' to your shell profile (.bashrc, .zshrc, etc.)"
else
    echo "❌ .nvmrc file not found. Please run script/setup.sh first."
    exit 1
fi
