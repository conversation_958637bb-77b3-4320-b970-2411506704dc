#!/bin/bash

# Rishi Core - Build Script
# This script builds the TypeScript project for production

set -e  # Exit on any error

echo "🔨 Building Rishi Core..."

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "❌ Dependencies not found. Please run ./setup.sh first."
    exit 1
fi

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist/*

# Build the project
echo "📦 Compiling TypeScript..."
npm run build

# Check if build was successful
if [ -f "dist/App.js" ]; then
    echo "✅ Build completed successfully!"
    echo "📁 Output files:"
    ls -la dist/
else
    echo "❌ Build failed - no output files found"
    exit 1
fi

echo ""
echo "🎉 Build process completed!"
echo "You can now run the project with: ./run.sh or npm start"
