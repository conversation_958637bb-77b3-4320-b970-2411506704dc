#!/bin/bash

# Rishi Core - Run Script
# This script builds and runs the Rishi Core application

set -e  # Exit on any error

echo "🚀 Starting Rishi Core..."

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "❌ Dependencies not found. Please run ./setup.sh first."
    exit 1
fi

# Build the project if dist doesn't exist or is empty
if [ ! -d "dist" ] || [ -z "$(ls -A dist 2>/dev/null)" ]; then
    echo "📦 No build found. Building project first..."
    ./build.sh
fi

# Check if the main file exists
if [ ! -f "dist/App.js" ]; then
    echo "❌ Built application not found. Running build..."
    ./build.sh
fi

echo "▶️  Running Rishi Core..."
echo "----------------------------------------"

# Run the application
npm start

echo "----------------------------------------"
echo "✅ Application finished"
